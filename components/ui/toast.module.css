:root {
   --animate-toast-leave: toast-leave var(--speed-slow) ease;
   --animate-toast-slide-in: toast-slide-in var(--speed-slow) ease;
}

.toastsGroup {
   position: fixed;
   top: 0;
   left: 0;
   display: grid;
   width: 100dvw;
   height: 100dvh;
   padding-top: var(--spacing);
   pointer-events: none;
   grid-template-rows: auto 1fr;

   &:empty {
      display: none;
   }
}

.toastContainer {
   /* Constrains the toast downward animation to a maximum of 2 toasts. */
   --inverse: calc(var(--toasts-count) - var(--toast-index));
   --clamped-inverse: min(var(--inverse), 3);
   --translate-y: calc((var(--clamped-inverse) - 1) * (100% + var(--toast-gap)));
   z-index: 50;
   display: grid;
   overflow: hidden;
   align-self: end;
   width: 100%;
   transition-timing-function: ease-in-out;
   transition-duration: var(--speed-slow);
   transition-property: translate, scale, opacity;
   animation: var(--animate-toast-slide-in);
   pointer-events: auto;
   translate: 0 var(--translate-y);

   grid-area: 1 / 1;
   grid-template-columns: 50dvw 100% 50dvw;
   gap: 100%;

   scrollbar-width: none;

   /* Selects any older toasts to shrink. */
   &:nth-last-child(n + 3) {
      --translate-y-shrunk: calc(
         var(--translate-y) +
         min((var(--inverse) * 2px), 20px)
      );
      translate: 0 var(--translate-y-shrunk);
   }

   &::-webkit-scrollbar {
      display: none;
   }

   @media (pointer: coarse) {
      overflow-x: scroll;
      overflow-y: visible;
      scroll-snap-type: x mandatory;
   }
}

.toastDismissMarker {
   width: 100%;
   height: 100%;
   scroll-snap-align: start;
   &:first-child {
      grid-area: 1 / 1;
   }

   &:last-child {
      grid-area: 1 / 3;
   }
}

.toast {
   position: relative;
   display: grid;
   width: calc(100dvw - (var(--spacing) * 2));
   max-width: calc(var(--spacing) * 20);
   min-height: calc(var(--spacing) * 2);
   padding: calc(var(--spacing) * 0.5);
   border-width: 2px;
   border-radius: 0.75rem;
   color: var(--canvas-text);
   background-color: var(--canvas-background);
   margin-inline: auto;
   grid-template-columns: 1fr auto;
   grid-template-rows: 1fr;
   gap: calc(var(--spacing) * 0.5);
   justify-self: center;
   scroll-snap-align: center;
   scroll-margin: var(--spacing);
   scroll-snap-stop: always;
   grid-area: 1 / 2;
}

.toastCloseButton {
   min-width: fit-content;
   padding: 0;
   border: none;
   color: var(--color-canvas-text);
   background-color: transparent;
   padding-inline: 0;
}

.toastCloseButtonIcon {
   width: var(--spacing);
   height: var(--spacing);
   rotate: 45deg;
}
