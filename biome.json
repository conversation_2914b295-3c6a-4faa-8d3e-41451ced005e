{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 3, "bracketSpacing": true}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "single", "arrowParentheses": "always"}}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"nursery": {"noNestedTernary": "error"}, "style": {"useBlockStatements": "error"}, "suspicious": {"useAwait": "error", "noConsole": "error"}, "correctness": {"noUnusedVariables": "error", "noUnusedFunctionParameters": "error", "noUnusedImports": "error", "noUndeclaredVariables": "error", "noUndeclaredDependencies": "error"}}}}