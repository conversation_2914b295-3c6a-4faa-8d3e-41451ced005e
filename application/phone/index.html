<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Document</title>
      <link rel="stylesheet" href="./source/styles.css" />
   </head>

   <body style="margin: 0">
      <div
         style="
            width: 100dvw;
            height: 100dvh;
            display: grid;
            place-items: center;
            scale: 0.95;
         "
      >
         <div class="device device-iphone-14-pro">
            <div class="device-frame" style="background-color: #272727">
               <div
                  class="device-status-bar"
                  style="
                     display: flex;
                     justify-content: space-between;
                     align-items: center;
                     position: absolute;
                     width: 388.4px;
                     height: 60px;
                     font-size: 16.5px;
                     font-family: 'Helvetica Neue';
                     border-top-left-radius: 47px;
                     border-top-right-radius: 47px;
                     padding-inline: 47px 35px;
                     background-color: #272727;
                     color: white;
                  "
               >
                  <span class="device-status-bar-left" style="color: white">
                     9:41
                  </span>
                  <span style="display: flex; gap: 8px">
                     <div>
                        <svg
                           fill="currentColor"
                           width="16px"
                           height="16px"
                           viewBox="0 0 512 512"
                           xmlns="http://www.w3.org/2000/svg"
                        >
                           <path
                              d="M472,432H424a24,24,0,0,1-24-24V104a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24V408A24,24,0,0,1,472,432Z"
                           />
                           <path
                              d="M344,432H296a24,24,0,0,1-24-24V184a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24V408A24,24,0,0,1,344,432Z"
                           />
                           <path
                              d="M216,432H168a24,24,0,0,1-24-24V248a24,24,0,0,1,24-24h48a24,24,0,0,1,24,24V408A24,24,0,0,1,216,432Z"
                           />
                           <path
                              d="M88,432H40a24,24,0,0,1-24-24V312a24,24,0,0,1,24-24H88a24,24,0,0,1,24,24v96A24,24,0,0,1,88,432Z"
                           />
                        </svg>
                     </div>
                     <div>
                        <svg
                           xmlns="http://www.w3.org/2000/svg"
                           xmlns:xlink="http://www.w3.org/1999/xlink"
                           width="17px"
                           height="17px"
                           viewBox="0 -2 14 14"
                           version="1.1"
                        >
                           <g
                              id="Page-1"
                              stroke="none"
                              stroke-width="1"
                              fill="none"
                              fill-rule="evenodd"
                           >
                              <g
                                 id="Dribbble-Light-Preview"
                                 transform="translate(-303.000000, -3684.000000)"
                                 fill="currentColor"
                              >
                                 <g
                                    id="icons"
                                    transform="translate(56.000000, 160.000000)"
                                 >
                                    <path
                                       d="M252.600198,3532.58575 L254,3533.99975 L255.399802,3532.58575 C254.626644,3531.80475 253.373356,3531.80475 252.600198,3532.58575 M249.800594,3529.75775 L251.200396,3531.17175 C252.743742,3529.61175 255.256258,3529.61175 256.800594,3531.17175 L258.200396,3529.75775 C255.880922,3527.41375 252.120068,3527.41375 249.800594,3529.75775 M261,3526.92875 L259.600198,3528.34275 C256.512516,3525.22375 251.488474,3525.22375 248.399802,3528.34275 L247,3526.92875 C250.86579,3523.02375 257.13421,3523.02375 261,3526.92875"
                                    ></path>
                                 </g>
                              </g>
                           </g>
                        </svg>
                     </div>
                     <div style="translate: 0 -2px">
                        <svg
                           xmlns="http://www.w3.org/2000/svg"
                           width="23px"
                           height="23px"
                           viewBox="0 0 512 512"
                        >
                           <title>ionicons-v5-d</title>
                           <rect
                              x="32"
                              y="144"
                              width="400"
                              height="224"
                              rx="45.7"
                              ry="45.7"
                              style="
                                 fill: none;
                                 stroke: currentColor;
                                 stroke-linecap: square;
                                 stroke-miterlimit: 10;
                                 stroke-width: 32px;
                              "
                           />
                           <rect
                              x="85.69"
                              y="198.93"
                              width="292.63"
                              height="114.14"
                              rx="4"
                              ry="4"
                              style="
                                 stroke: currentColor;
                                 stroke-linecap: square;
                                 stroke-miterlimit: 10;
                                 stroke-width: 32px;
                                 fill: currentColor;
                              "
                           />
                           <line
                              x1="480"
                              y1="218.67"
                              x2="480"
                              y2="293.33"
                              style="
                                 fill: none;
                                 stroke: currentColor;
                                 stroke-linecap: round;
                                 stroke-miterlimit: 10;
                                 stroke-width: 32px;
                              "
                           />
                        </svg>
                     </div>
                  </span>
               </div>
               <iframe
                  style="
                     width: 100%;
                     height: 100%;
                     border-radius: 47px;
                     zoom: 0.9;
                     padding-top: calc(60px + 2%);
                  "
                  src="http://localhost:5229/sidebar"
                  frameborder="0"
                  scrolling="yes"
               ></iframe>
            </div>
            <div class="device-stripe"></div>
            <div class="device-header"></div>
            <div class="device-sensors"></div>
            <div class="device-btns"></div>
            <div class="device-power"></div>
         </div>
      </div>
   </body>
</html>
